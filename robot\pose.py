from __future__ import annotations
from dataclasses import dataclass
from typing import Iterable, Sequence, Union, overload, List, Tuple, Literal
from typing_extensions import Self
import numpy as np
from numpy.typing import NDArray
from scipy.spatial.transform import Rotation as SciRot, Slerp

ArrayLike = Union[NDArray[np.floating], Iterable[float], Sequence[float]]


# ------------------------------ Point ------------------------------


@dataclass(frozen=True)
class Point:
    """A 3D point (position) expressed in some frame. Not a transform."""

    p: NDArray[np.floating]  # shape (3,)

    def __init__(self, p: ArrayLike):
        p_ = np.asarray(p, dtype=float).reshape(3)
        object.__setattr__(self, "p", p_)

    @staticmethod
    def origin() -> "Point":
        return Point([0.0, 0.0, 0.0])

    def as_array(self) -> NDArray[np.floating]:
        return self.p.copy()

    def to_homogeneous(self) -> NDArray[np.floating]:
        return np.array([self.p[0], self.p[1], self.p[2], 1.0], dtype=float)

    # Allow: Pose @ Point (handled here via right-matmul)
    def __rmatmul__(self, T) -> "Point":
        if hasattr(T, "apply"):
            return Point(T.apply(self.p))
        T = np.asarray(T, dtype=float)
        if T.shape == (4, 4):
            return Point(T[:3, :3] @ self.p + T[:3, 3])
        raise TypeError("Left operand must be a Pose-like with .apply() or a 4x4 SE(3) matrix.")

    # Point +- translation vector
    def __add__(self, v: ArrayLike) -> "Point":
        v = np.asarray(v, dtype=float).reshape(3)
        return Point(self.p + v)

    def __sub__(self, other: Union["Point", ArrayLike]):
        if isinstance(other, Point):
            return self.p - other.p  # displacement vector
        v = np.asarray(other, dtype=float).reshape(3)
        return Point(self.p - v)

    def distance_to(self, other: "Point") -> float:
        return float(np.linalg.norm(self.p - other.p))

    def lerp(self, other: "Point", alpha: float) -> "Point":
        a = float(alpha)
        return Point((1.0 - a) * self.p + a * other.p)


# ------------------------------ Pose ------------------------------


@dataclass(frozen=True)
class Pose:
    """
    SE(3) pose with translation `t` (shape (3,)) and orientation `R` (scipy Rotation).
    Composition (matrix semantics): self @ other
      - Pose @ Pose -> Pose
      - Pose @ Point -> Point
    """

    t: NDArray[np.floating]
    R: SciRot

    # ---- Basics ----
    @staticmethod
    def identity() -> "Pose":
        return Pose(np.zeros(3, dtype=float), SciRot.identity())

    # ---- Constructors (with overloads to accept one concatenated array) ----

    @overload
    @staticmethod
    def from_xyz_rotvec(xyz: ArrayLike, rotvec: ArrayLike) -> "Pose": ...
    @overload
    @staticmethod
    def from_xyz_rotvec(xyz_rotvec: ArrayLike, /) -> "Pose": ...
    @staticmethod
    def from_xyz_rotvec(xyz: ArrayLike, rotvec: ArrayLike | None = None) -> "Pose":
        """
        Accepts:
          - (xyz, rotvec) -> lengths 3+3
          - single array/list of length 6 -> [x,y,z, rx,ry,rz]
        """
        if rotvec is None:
            arr = np.asarray(xyz, dtype=float).reshape(6)
            t = arr[:3]
            r = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            r = np.asarray(rotvec, dtype=float).reshape(3)
        return Pose(t, SciRot.from_rotvec(r))

    @overload
    @staticmethod
    def from_xyz_euler(
        xyz: ArrayLike, euler: ArrayLike, *, seq: str = "zyx", degrees: bool = False
    ) -> "Pose": ...
    @overload
    @staticmethod
    def from_xyz_euler(
        xyz_euler: ArrayLike, /, *, seq: str = "zyx", degrees: bool = False
    ) -> "Pose": ...
    @staticmethod
    def from_xyz_euler(
        xyz: ArrayLike, euler: ArrayLike | None = None, *, seq: str = "zyx", degrees: bool = False
    ) -> "Pose":
        """
        Accepts:
          - (xyz, euler) -> 3+3
          - single length-6 array -> [x,y,z, e1,e2,e3] in given `seq`
        """
        if euler is None:
            arr = np.asarray(xyz, dtype=float).reshape(6)
            t = arr[:3]
            e = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            e = np.asarray(euler, dtype=float).reshape(3)
        return Pose(t, SciRot.from_euler(seq, e, degrees=degrees))

    @overload
    @staticmethod
    def from_xyz_quat(
        xyz: ArrayLike, quat: ArrayLike, *, order: Literal["xyzw", "wxyz"] = "xyzw"
    ) -> "Pose": ...
    @overload
    @staticmethod
    def from_xyz_quat(
        xyz_quat: ArrayLike, /, *, order: Literal["xyzw", "wxyz"] = "xyzw"
    ) -> "Pose": ...
    @staticmethod
    def from_xyz_quat(
        xyz: ArrayLike, quat: ArrayLike | None = None, *, order: Literal["xyzw", "wxyz"] = "xyzw"
    ) -> "Pose":
        """
        Accepts:
          - (xyz, quat) -> 3 + 4
          - single length-7 array -> [x,y,z, q*] in given `order`
        """
        if quat is None:
            arr = np.asarray(xyz, dtype=float).reshape(7)
            t = arr[:3]
            q = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            q = np.asarray(quat, dtype=float).reshape(4)
        if order == "wxyz":
            q = np.array([q[1], q[2], q[3], q[0]], dtype=float)
        return Pose(t, SciRot.from_quat(q))  # SciPy expects xyzw

    @staticmethod
    def from_RT(R: ArrayLike, t: ArrayLike) -> "Pose":
        return Pose(
            np.asarray(t, dtype=float).reshape(3),
            SciRot.from_matrix(np.asarray(R, dtype=float).reshape(3, 3)),
        )

    @staticmethod
    def from_SE3(T: ArrayLike) -> "Pose":
        T = np.asarray(T, dtype=float).reshape(4, 4)
        return Pose.from_RT(T[:3, :3], T[:3, 3])

    # ---- Converters ----
    def as_xyz_rotvec(self) -> NDArray[np.floating]:
        return np.hstack((self.t, self.R.as_rotvec()))

    def as_xyz_euler(self, seq: str = "zyx", degrees: bool = False) -> NDArray[np.floating]:
        return np.hstack((self.t, self.R.as_euler(seq, degrees=degrees)))

    def as_xyz_quat(self, order: Literal["xyzw", "wxyz"] = "xyzw") -> NDArray[np.floating]:
        q = self.R.as_quat()  # xyzw
        if order == "wxyz":
            q = np.array([q[3], q[0], q[1], q[2]])
        return np.hstack((self.t, q))

    def as_RT(self) -> Tuple[NDArray[np.floating], NDArray[np.floating]]:
        return self.R.as_matrix(), self.t.copy()

    def as_SE3(self) -> NDArray[np.floating]:
        T = np.eye(4)
        T[:3, :3] = self.R.as_matrix()
        T[:3, 3] = self.t
        return T

    # ---- Core ops ----
    @overload
    def __matmul__(self: Self, other: "Pose") -> "Pose": ...
    @overload
    def __matmul__(self: Self, other: Point) -> Point: ...
    def __matmul__(self, other):
        if isinstance(other, Pose):
            R_new = self.R * other.R
            t_new = self.t + self.R.apply(other.t)
            return Pose(t_new, R_new)
        if isinstance(other, Point):
            return Point(self.R.apply(other.p) + self.t)
        return NotImplemented

    def inverse(self) -> "Pose":
        R_inv = self.R.inv()
        return Pose(-R_inv.apply(self.t), R_inv)

    # Convenience: change frame vs local offset
    def left_multiply(self, H: Union["Pose", ArrayLike]) -> "Pose":
        Hpose = H if isinstance(H, Pose) else Pose.from_SE3(H)
        return Hpose @ self

    def right_multiply(self, H: Union["Pose", ArrayLike]) -> "Pose":
        Hpose = H if isinstance(H, Pose) else Pose.from_SE3(H)
        return self @ Hpose

    # Apply to numpy array(s) of points
    def apply(self, pts: ArrayLike) -> NDArray[np.floating]:
        P = np.asarray(pts, dtype=float)
        if P.ndim == 1:
            return self.R.apply(P) + self.t
        if P.ndim == 2 and P.shape[1] == 3:
            return self.R.apply(P) + self.t
        raise ValueError("pts must be shape (3,) or (N,3)")

    # ---- SLERP (typed: scalar -> Pose, iterable -> list[Pose]) ----
    @overload
    def slerp(self: Self, other: Self, alpha: float) -> Self: ...
    @overload
    def slerp(self: Self, other: Self, alpha: Iterable[float]) -> List[Self]: ...
    def slerp(self, other, alpha):
        key_times = np.array([0.0, 1.0])
        rot_key = SciRot.from_quat(np.vstack([self.R.as_quat(), other.R.as_quat()]))  # xyzw
        slerp_fn = Slerp(key_times, rot_key)

        is_scalar = isinstance(alpha, (int, float))
        alphas = (
            np.array([alpha], dtype=float) if is_scalar else np.asarray(list(alpha), dtype=float)
        )

        t_interp = (1.0 - alphas)[:, None] * self.t[None, :] + alphas[:, None] * other.t[None, :]
        R_interp = slerp_fn(alphas)

        poses = [type(self)(t_interp[i], R_interp[i]) for i in range(len(alphas))]
        return poses[0] if is_scalar else poses


if __name__ == "__main__":
    p1 = Pose.from_xyz_rotvec([0, 0, 0, 0, 0, 0])
    p2 = Pose.from_xyz_rotvec([1, 1, 1, 0, 0, np.pi / 2])
    p1.slerp(p2, 0.5)
