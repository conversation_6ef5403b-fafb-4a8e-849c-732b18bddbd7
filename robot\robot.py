# robot protocol

from contextlib import contextmanager
from typing import Protocol
from dataclasses import dataclass

from robot.pose import Pose


@dataclass
class Joints:
    j1: float
    j2: float
    j3: float
    j4: float
    j5: float
    j6: float

    def __add__(self, other: "Joints") -> "Joints":
        return Joints(
            j1=self.j1 + other.j1,
            j2=self.j2 + other.j2,
            j3=self.j3 + other.j3,
            j4=self.j4 + other.j4,
            j5=self.j5 + other.j5,
            j6=self.j6 + other.j6,
        )

    def __sub__(self, other: "Joints") -> "Joints":
        return Joints(
            j1=self.j1 - other.j1,
            j2=self.j2 - other.j2,
            j3=self.j3 - other.j3,
            j4=self.j4 - other.j4,
            j5=self.j5 - other.j5,
            j6=self.j6 - other.j6,
        )

    def to_list(self) -> list[float]:
        return [self.j1, self.j2, self.j3, self.j4, self.j5, self.j6]


class Robot(Protocol):
    def is_ready(self) -> bool: ...

    def movel(self, pose: Pose | Joints, a: float, v: float, block: bool = True) -> None: ...

    def movej(self, joints: Joints, a: float, v: float, block: bool = True) -> None: ...

    def movel_relative(
        self,
        pose_delta: Pose,
        a: float,
        v: float,
        block: bool = True,
    ) -> None: ...

    def movej_relative(
        self,
        joints_delta: Joints,
        a: float,
        v: float,
        block: bool = True,
    ) -> None: ...

    def get_pose(self) -> Pose: ...

    def get_joints(self) -> Joints: ...

    def get_joint_velocities(self) -> list[float]: ...

    def is_running(self) -> bool: ...

    def wait_until_stopped(self) -> None: ...

    @contextmanager
    def connection(self):
        """
        Keeps all necessary connections open.
        """
        yield self
