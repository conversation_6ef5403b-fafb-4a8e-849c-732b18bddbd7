import socket
import struct
import threading
from contextlib import contextmanager
from dataclasses import dataclass
from functools import singledispatchmethod
from time import monotonic, sleep
from typing import Optional

import keepalive

from robot.pose import Pose
from robot.robot import Joints, Robot


@dataclass
class URStatus:
    timestamp: int
    is_real_robot_connected: bool
    is_real_robot_enabled: bool
    is_robot_power_on: bool
    is_emergency_stopped: bool
    is_protective_stopped: bool
    is_program_running: bool
    is_program_paused: bool
    robot_mode: int
    control_mode: int
    target_speed_fraction: float
    speed_scaling: float
    target_speed_fraction_limit: float


class URRobot(Robot):
    """
    Universal Robot implementation of the Robot protocol.

    This class provides control and monitoring capabilities for UR robots.

    Improvements:
    - Optional persistent streaming connection to the secondary interface
      to avoid connect/disconnect on every read.
    - Smarter wait_until_stopped that avoids busy-wait and initial sleeps.
    """

    def __init__(
        self,
        robot_ip: str = "***************",
        default_acceleration_l: float = 1.2,
        default_velocity_l: float = 0.5,
        default_acceleration_j: float = 0.5,
        default_velocity_j: float = 0.25,
    ):
        """
        Initialize the UR robot.

        Args:
            robot_ip: IP address of the UR robot (default: "***************")
        """
        self.robot_ip = robot_ip
        self.primary_port = 30001  # For sending commands
        self.secondary_port = 30002  # For reading state
        self.default_acceleration_l = default_acceleration_l  # m/s² for movel
        self.default_velocity_l = default_velocity_l  # m/s for movel
        self.default_acceleration_j = default_acceleration_j  # rad/s² for movej
        self.default_velocity_j = default_velocity_j  # rad/s for movej

        # Streaming state (optional persistent connection to secondary interface)
        self._state_socket: Optional[socket.socket] = None
        self._state_thread: Optional[threading.Thread] = None
        self._state_lock = threading.Lock()
        self._streaming = False
        self._latest_state_bytes: Optional[bytes] = None
        self._stop_stream_event = threading.Event()

        # Optional persistent control connection (primary interface)
        self._control_socket: Optional[socket.socket] = None

    def is_ready(self) -> bool:
        """Check if the robot is ready for movement."""
        return self.get_status().robot_mode == 7

    @singledispatchmethod
    def movel(self, pose: Pose, a: float = None, v: float = None, block=True):
        """
        Move the robot to a pose using linear movement.

        Args:
            pose: Target pose (x, y, z, rx, ry, rz)
        """
        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration_l
        if v is None:
            v = self.default_velocity_l

        # Create URScript movel command
        command = f"movel(p{pose.as_xyz_rotvec()}, a={a}, v={v})"

        self._send_urscript_command(command, block=block)

    @movel.register
    def _(self, pose: Joints, a: float = None, v: float = None, block=True):
        """
        Move the robot to a pose using linear movement.

        Args:
            pose: Target pose (x, y, z, rx, ry, rz)
        """
        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration_j
        if v is None:
            v = self.default_velocity_j

        # Create URScript movel command
        command = f"movel({pose.to_list()}, a={a}, v={v})"

        self._send_urscript_command(command, block=block)

    def movej(self, joints: Joints, a: float = None, v: float = None, block=True):
        """
        Move the robot to joint positions.

        Args:
            joints: Target joint positions in radians
        """
        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration_j
        if v is None:
            v = self.default_velocity_j

        # Create URScript movej command
        command = f"movej({joints.to_list()}, a={a}, v={v})"

        self._send_urscript_command(command, block=block)

    def movep(self, pose: Pose3D, r: float = 0.1, a: float = None, v: float = None, block=False):
        """
        Move the robot along a path with blending.

        Args:
            pose: Target pose (x, y, z, rx, ry, rz)
            r: Blend radius
        """
        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration_l
        if v is None:
            v = self.default_velocity_l

        # Create URScript movep command
        command = f"movep(p{pose.to_list()}, a={a}, v={v}, r={r})"

        self._send_urscript_command(command, block=block)

    def movel_relative(self, pose_delta: Pose3D, a: float = None, v: float = None, block=True):
        """
        Move the robot relative to current pose using linear movement.

        Args:
            pose_delta: Relative pose change (dx, dy, dz, drx, dry, drz)
        """
        # Get current pose
        current_pose = self.get_pose()

        # Calculate target pose by adding delta
        target_pose = current_pose + pose_delta

        # Execute absolute movement to target pose
        self.movel(target_pose, a, v, block)

    def movej_relative(self, joints_delta: Joints, a: float = None, v: float = None, block=True):
        """
        Move the robot relative to current joint positions.

        Args:
            joints_delta: Relative joint changes (dj1, dj2, dj3, dj4, dj5, dj6)
        """
        # Get current joint positions
        current_joints = self.get_joints()

        # Calculate target joints by adding delta
        target_joints = current_joints + joints_delta

        # Execute absolute movement to target joints
        self.movej(target_joints, a, v, block)

    def do_dance(self):
        script_text = (
            "def test_move():\n"
            "   global P_start_p=p[0.439, 0.687, 1.03, -0.0, 0.0, 0.0]\n"
            "   global P_mid_p=p[0.239, 0.687, 1.03, -0.0, 0.0, 0.0]\n"
            "   global P_end_p=p[0.439, 0.687, 0.93, -0.0, 0.0, 0.0]\n"
            "   while (True):\n"
            "     movel(P_start_p, a=1.2, v=0.25)\n"
            "     movel(P_mid_p, a=1.2, v=0.25)\n"
            "     movel(P_end_p, a=1.2, v=0.25)\n"
            "   end\n"
            "end\n"
        )

        self._send_urscript_command(script_text)

    def get_pose(self) -> Pose:
        """
        Get current TCP pose.

        Returns:
            Current TCP pose (x, y, z, rx, ry, rz)
        """
        data = self._read_robot_state()
        return self._parse_tcp_pose(data)

    def get_joints(self) -> Joints:
        """
        Get current joint positions.

        Returns:
            Current joint positions in radians
        """
        data = self._read_robot_state()
        return self._parse_joint_positions(data)

    def get_joint_velocities(self) -> list[float]:
        """
        Get current joint velocities from robot state data.

        Returns:
            List of 6 joint velocities in rad/s
        """
        data = self._read_robot_state()
        return self._parse_joint_velocities(data)

    def get_status(self) -> URStatus:
        """Get current robot status."""
        data = self._read_robot_state()
        return self._parse_robot_status(data)

    def is_running(self) -> bool:
        """Return True if a program is running."""
        return self.get_status().is_program_running

    def wait_until_stopped(self) -> None:
        """
        Wait until the program is stopped.
        """
        with self.state_stream():
            deadline = monotonic() + 0.2
            # Wait for first indication of motion
            while (not self.is_running()) and monotonic() < deadline:
                sleep(0.001)
            while self.is_running():
                sleep(0.001)

    def _send_urscript_command(self, command: str, block: bool = True) -> None:
        """
        Send a URScript command to the robot. If a persistent control_connection
        is active, reuse it. Otherwise, make an ad-hoc connection.

        Args:
            command: URScript command to send
        """
        try:
            command_with_newline = command + "\n"
            if self._control_socket is None:
                # Ad-hoc connection
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as cmd_socket:
                    cmd_socket.settimeout(10.0)  # 10 second timeout
                    cmd_socket.connect((self.robot_ip, self.primary_port))
                    # Send the command
                    cmd_socket.sendall(command_with_newline.encode("utf-8"))
                    # Read response (at least 79 bytes as per documentation)
                    _ = cmd_socket.recv(1024)  # Read and discard response
            else:
                # Use persistent control connection
                sock = self._control_socket
                sock.sendall(command_with_newline.encode("utf-8"))
                _ = sock.recv(1024)  # Read and discard response

            print(f"Command sent successfully: {command}")

            if block:
                self.wait_until_stopped()

        except Exception as e:
            raise RuntimeError(f"Failed to send command '{command}': {e}")

    def _read_robot_state(self) -> bytes:
        """
        Read robot state data from the secondary port or the streaming cache.

        Returns:
            Raw robot state data as bytes
        """
        # If streaming is active, return the latest cached robot state
        if self._streaming:
            # Wait briefly for data to be available
            deadline = monotonic() + 1.0
            while True:
                with self._state_lock:
                    data = self._latest_state_bytes
                if data is not None:
                    return data
                if monotonic() > deadline or self._stop_stream_event.is_set():
                    break
                sleep(0.01)
            # If we couldn't get from stream, fall back to direct read

        try:
            # Connect to secondary port for reading state (one-shot)
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as state_socket:
                state_socket.settimeout(10.0)  # 10 second timeout
                state_socket.connect((self.robot_ip, self.secondary_port))

                # Read multiple messages until we find a robot state message (type 16)
                for _ in range(10):  # Try up to 10 messages
                    # Read the message size first (4 bytes, big-endian integer)
                    size_data = state_socket.recv(4)
                    if len(size_data) != 4:
                        raise RuntimeError("Failed to read message size")

                    message_size = struct.unpack(">I", size_data)[0]

                    # Read the rest of the message
                    remaining_data = state_socket.recv(message_size - 4)

                    full_data = size_data + remaining_data

                    # Check if this is a robot state message (type 16)
                    if len(full_data) >= 5:
                        message_type = struct.unpack(">B", full_data[4:5])[0]
                        if message_type == 16:  # Robot state message
                            return full_data

                    # If not robot state, continue to next message
                    # (Skip version messages, safety messages, etc.)

                raise RuntimeError("No robot state message found after 10 attempts")

        except Exception as e:
            raise RuntimeError(f"Failed to read robot state: {e}")

    def _parse_robot_status(self, data: bytes) -> URStatus:
        """
        Parse robot status from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Robot status
        """
        try:
            offset = 5  # after message type
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]
                if package_type == 0:  # Robot mode data (see docs)
                    pkg_start = offset + 5
                    if package_size >= 30:  # 5 hdr + 25 bytes content
                        timestamp = struct.unpack(">Q", data[pkg_start : pkg_start + 8])[0]
                        is_real_robot_connected = bool(data[pkg_start + 8])
                        is_real_robot_enabled = bool(data[pkg_start + 9])
                        is_robot_power_on = bool(data[pkg_start + 10])
                        is_emergency_stopped = bool(data[pkg_start + 11])
                        is_protective_stopped = bool(data[pkg_start + 12])
                        is_program_running = bool(data[pkg_start + 13])
                        is_program_paused = bool(data[pkg_start + 14])
                        robot_mode = data[pkg_start + 15]
                        control_mode = data[pkg_start + 16]
                        target_speed_fraction = struct.unpack(
                            ">d", data[pkg_start + 17 : pkg_start + 25]
                        )[0]
                        speed_scaling = struct.unpack(">d", data[pkg_start + 25 : pkg_start + 33])[
                            0
                        ]
                        target_speed_fraction_limit = struct.unpack(
                            ">d", data[pkg_start + 33 : pkg_start + 41]
                        )[0]
                        return URStatus(
                            timestamp=timestamp,
                            is_real_robot_connected=is_real_robot_connected,
                            is_real_robot_enabled=is_real_robot_enabled,
                            is_robot_power_on=is_robot_power_on,
                            is_emergency_stopped=is_emergency_stopped,
                            is_protective_stopped=is_protective_stopped,
                            is_program_running=is_program_running,
                            is_program_paused=is_program_paused,
                            robot_mode=robot_mode,
                            control_mode=control_mode,
                            target_speed_fraction=target_speed_fraction,
                            speed_scaling=speed_scaling,
                            target_speed_fraction_limit=target_speed_fraction_limit,
                        )
                offset += package_size
            raise RuntimeError("Robot mode data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse robot status: {e}")

    def _parse_robot_mode_running(self, data: bytes) -> Optional[bool]:
        """Best-effort parse of whether the program/robot is running from Robot Mode Data.
        Returns True/False if parsed, or None if unavailable.
        Note: UR protocol specifics vary by version; this is a minimal, best-effort approach.
        """
        try:
            offset = 5  # after message type
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]
                if package_type == 0:  # Robot mode data (see docs)
                    # Layout (after size+type = 5 bytes):
                    # [0..7]=timestamp (uint64), [8]=isRealRobotConnected, [9]=isRealRobotEnabled,
                    # [10]=isRobotPowerOn, [11]=isEmergencyStopped, [12]=isProtectiveStopped,
                    # [13]=isProgramRunning, [14]=isProgramPaused, [15]=robotMode,
                    # [16]=controlMode, [17..24]=targetSpeedFraction (double), ...
                    pkg_start = offset + 5
                    # Ensure we have at least up to byte 13 in the content
                    if package_size >= 19:  # 5 header + 14 content bytes
                        flag = data[pkg_start + 13]
                        print(f"Robot mode running: {bool(flag)}")
                        return bool(flag)
                    return None
                offset += package_size
            return None
        except Exception:
            print("Failed to parse robot mode running")
            return None

    def _parse_tcp_pose(self, data: bytes) -> Pose:
        """
        Parse TCP pose from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current TCP pose
        """
        try:
            # Skip to cartesian info section
            offset = 5  # Skip message type

            # Find cartesian info package (type 4)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 4:  # Cartesian info package
                    # TCP pose starts at offset 5 within the cartesian package
                    pose_offset = offset + 5

                    # Read 6 pose values (8 bytes each, double precision)
                    pose_data: list[float] = []
                    for i in range(6):
                        pose_val = struct.unpack(
                            ">d", data[pose_offset + i * 8 : pose_offset + (i + 1) * 8]
                        )[0]
                        pose_data.append(pose_val)

                    return Pose.from_xyz_rotvec(pose_data)

                offset += package_size

            raise RuntimeError("Cartesian info not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse TCP pose: {e}")

    def _parse_joint_positions(self, data: bytes) -> Joints:
        """
        Parse joint positions from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current joint positions
        """
        try:
            # Skip to joint data section
            # The joint data starts after the robot mode data
            # Robot mode data is typically at offset 5 (1 byte message type + 4 bytes size)
            # Joint data package starts after robot mode data package

            offset = 5  # Skip message type

            # Find joint data package (type 1)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 1:  # Joint data package
                    # Joint data starts at offset 5 within the joint package (after size + type)
                    joint_data_start = offset + 5

                    # Each joint has: q_actual(8) + q_target(8) + qd_actual(8) + I_actual(4) + V_actual(4) + T_motor(4) + T_micro(4) + jointMode(1) = 41 bytes
                    joint_size = 41

                    # Read 6 joint positions (only q_actual, the first 8 bytes of each joint)
                    joints_data = []
                    for i in range(6):
                        joint_offset = joint_data_start + i * joint_size
                        # Read only q_actual (first 8 bytes of each joint)
                        joint_pos = struct.unpack(
                            ">d",
                            data[joint_offset : joint_offset + 8],
                        )[0]
                        joints_data.append(joint_pos)

                    return Joints(
                        j1=joints_data[0],
                        j2=joints_data[1],
                        j3=joints_data[2],
                        j4=joints_data[3],
                        j5=joints_data[4],
                        j6=joints_data[5],
                    )

                offset += package_size

            raise RuntimeError("Joint data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse joint positions: {e}")

    def _parse_joint_velocities(self, data: bytes) -> list[float]:
        """
        Parse joint velocities from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            List of current joint velocities in rad/s
        """
        try:
            # Skip to joint data section
            offset = 5  # Skip message type

            # Find joint data package (type 1)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 1:  # Joint data package
                    # Joint data starts at offset 5 within the joint package (after size + type)
                    joint_data_start = offset + 5

                    # Each joint has: q_actual(8) + q_target(8) + qd_actual(8) + I_actual(4) + V_actual(4) + T_motor(4) + T_micro(4) + jointMode(1) = 41 bytes
                    joint_size = 41

                    # Read 6 joint velocities (qd_actual, which is at offset 16 within each joint)
                    velocities = []
                    for i in range(6):
                        joint_offset = joint_data_start + i * joint_size
                        # Skip q_actual (8 bytes) and q_target (8 bytes) to get to qd_actual
                        velocity_offset = joint_offset + 16
                        velocity = struct.unpack(
                            ">d",
                            data[velocity_offset : velocity_offset + 8],
                        )[0]
                        velocities.append(velocity)

                    return velocities

                offset += package_size

            raise RuntimeError("Joint data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse joint velocities: {e}")

    def start_state_stream(self) -> None:
        """Start a background thread that keeps a persistent connection to the
        secondary interface and continuously updates the latest robot state bytes.
        Safe to call multiple times.
        """
        if self._streaming:
            return
        # Reset flags
        self._stop_stream_event.clear()
        # Establish socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect((self.robot_ip, self.secondary_port))
        # Switch to blocking mode for streaming
        sock.settimeout(None)
        self._state_socket = sock
        self._streaming = True
        self._state_thread = threading.Thread(
            target=self._state_reader_loop, name="URStateReader", daemon=True
        )
        self._state_thread.start()

    def stop_state_stream(self) -> None:
        """Stop the background streaming thread and close the socket."""
        if not self._streaming:
            return
        self._stop_stream_event.set()
        try:
            if self._state_socket is not None:
                try:
                    self._state_socket.shutdown(socket.SHUT_RDWR)
                except Exception:
                    pass
                self._state_socket.close()
        finally:
            self._state_socket = None
        if self._state_thread is not None:
            self._state_thread.join(timeout=1.0)
        self._state_thread = None
        self._streaming = False

    @contextmanager
    def state_stream(self):
        """Context manager to keep the state stream open for the duration of the block."""
        if self._streaming:
            # Already streaming, no-op
            yield self
            return
        self.start_state_stream()
        try:
            yield self
        finally:
            self.stop_state_stream()

    @contextmanager
    def control_connection(self):
        """Context manager to keep a persistent control connection (primary port)
        open for the duration of the block. When active, _send_urscript_command
        reuses this connection; otherwise it makes an ad-hoc connection.
        """
        if self._control_socket is not None:
            # Already connected, no-op
            yield self
            return
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.settimeout(10.0)
            sock.connect((self.robot_ip, self.primary_port))
            keepalive.set(sock)
            self._control_socket = sock
            try:
                yield self
            finally:
                try:
                    if self._control_socket is not None:
                        try:
                            self._control_socket.shutdown(socket.SHUT_RDWR)
                        except Exception:
                            pass
                        self._control_socket.close()
                finally:
                    self._control_socket = None
        except Exception:
            # Ensure socket is closed on failure
            try:
                sock.close()
            except Exception:
                pass
            raise

    @contextmanager
    def connection(self):
        """Context manager to keep both control and state connections open
        for the duration of the block.
        """
        with self.control_connection(), self.state_stream():
            yield self

    def _recv_exact(self, sock: socket.socket, n: int) -> bytes:
        """Receive exactly n bytes from the socket or raise RuntimeError."""
        chunks = []
        remaining = n
        while remaining > 0:
            chunk = sock.recv(remaining)
            if not chunk:
                raise RuntimeError("Socket connection broken while receiving data")
            chunks.append(chunk)
            remaining -= len(chunk)
        return b"".join(chunks)

    def _state_reader_loop(self) -> None:
        """Continuously read messages from the secondary interface and cache the
        latest full robot state message (type 16).
        """
        sock = self._state_socket
        if sock is None:
            return
        try:
            while not self._stop_stream_event.is_set():
                # Each message: 4 bytes size, then (size-4) bytes payload
                size_data = self._recv_exact(sock, 4)
                message_size = struct.unpack(">I", size_data)[0]
                remaining = self._recv_exact(sock, message_size - 4)
                full_data = size_data + remaining
                if len(full_data) >= 5:
                    message_type = struct.unpack(">B", full_data[4:5])[0]
                    if message_type == 16:
                        with self._state_lock:
                            self._latest_state_bytes = full_data
        except Exception:
            # On any error, stop streaming cleanly
            pass
        finally:
            try:
                if self._state_socket is not None:
                    self._state_socket.close()
            except Exception:
                pass
            self._state_socket = None
            self._streaming = False


if __name__ == "__main__":
    robot = URRobot()
    with robot.connection():
        print(robot.get_status())
        # print(robot.get_joints())
        # print(robot.get_pose())
        start_pose = robot.get_pose()
        # robot.movel_relative(Pose3D(x=0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.movel_relative(Pose3D(x=-0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=-0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=-0.2, rx=0.0, ry=0.0, rz=0.0))

        # robot.movep(start_pose + Pose3D(x=0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movep(start_pose + Pose3D(x=0.2, y=0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.movep(start_pose + Pose3D(x=0.2, y=0.2, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.movep(start_pose + Pose3D(x=0.0, y=0.2, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.movep(start_pose + Pose3D(x=0.0, y=0.0, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.movep(start_pose + Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))

        # robot.do_dance()
        # robot.wait_until_stopped()

        # robot.begin_path()
        # robot.add_linear(start_pose + Pose3D(x=0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.add_linear(start_pose + Pose3D(x=0.2, y=0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.add_linear(start_pose + Pose3D(x=0.2, y=0.2, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.add_linear(start_pose + Pose3D(x=0.0, y=0.2, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.add_linear(start_pose + Pose3D(x=0.0, y=0.0, z=0.2, rx=0.0, ry=0.0, rz=0.0))
        # robot.add_linear(start_pose + Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
        # robot.execute_path()
        # robot.wait_until_stopped()

        # target_pose = robot.get_pose()
        # target_pose.rx = 0
        # target_pose.ry = 0
        # target_pose.rz = 0
        # robot.movel(target_pose)

        robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=0.4, ry=0.0, rz=0.0))
        robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=-0.4, ry=0.0, rz=0.0))
        # # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.4, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=-0.4, rz=0.0))
        # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.4))
        # robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=-0.4))
        end_pose = robot.get_pose()
        print(f"Error on path: {end_pose - start_pose}")
